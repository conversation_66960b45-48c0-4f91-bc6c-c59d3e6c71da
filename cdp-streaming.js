import { CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

// Pure CDP implementation for screen streaming POC
class CDPStreamingManager {
  constructor() {
    this.cdp = null;
    this.targetId = null;
    this.sessionId = null;
    this.targets = new Map(); // Track multiple targets
  }

  async connect() {
    try {
      // Get browser WebSocket endpoint
      const response = await fetch("http://localhost:9222/json/version");
      const data = await response.json();
      const wsUrl = data.webSocketDebuggerUrl;

      console.log("🔗 Connecting to CDP:", wsUrl);

      this.cdp = new CDP({ webSocketDebuggerUrl: wsUrl });

      // Listen for target events
      this.cdp.Target.targetCreated = (params) => {
        console.log("🎯 Target created:", params.targetInfo);
        this.handleNewTarget(params.targetInfo);
      };

      this.cdp.Target.targetDestroyed = (params) => {
        console.log("💥 Target destroyed:", params.targetId);
        this.targets.delete(params.targetId);
      };

      // Enable target domain
      await this.cdp.Target.setDiscoverTargets({ discover: true });

      console.log("✅ CDP connected successfully");
      return true;
    } catch (error) {
      console.error("❌ CDP connection failed:", error);
      return false;
    }
  }

  async createNewTarget(url = "about:blank") {
    try {
      console.log("🎯 Creating new browser target...");
      const target = await this.cdp.Target.createTarget({ url });
      this.targetId = target.targetId;
      console.log(`✅ Target created with ID: ${this.targetId}`);

      // Attach to the target
      const { sessionId } = await this.cdp.Target.attachToTarget({
        targetId: this.targetId,
        flatten: true,
      });
      this.sessionId = sessionId;
      console.log(`🔗 Attached to target with session: ${sessionId}`);

      // Store target info
      this.targets.set(this.targetId, {
        sessionId,
        url,
        hasStreaming: false,
      });

      return { targetId: this.targetId, sessionId };
    } catch (error) {
      console.error("❌ Failed to create target:", error);
      throw error;
    }
  }

  async injectStreamingScript(targetId, sessionId) {
    try {
      console.log(`💉 Injecting streaming script into target: ${targetId}`);

      // Enable Runtime domain for this session
      await this.cdp.Runtime.enable({ sessionId });

      // Enable Page domain for navigation events
      await this.cdp.Page.enable({ sessionId });

      // Script to inject getDisplayMedia functionality
      const streamingScript = `
        (function() {
          console.log('🎬 Streaming script injected!');
          
          window.streamingManager = {
            currentStream: null,
            
            async startDisplayCapture() {
              try {
                console.log('🚀 Starting display capture...');
                const stream = await navigator.mediaDevices.getDisplayMedia({
                  video: {
                    mediaSource: 'screen',
                    width: { ideal: 1920 },
                    height: { ideal: 1080 },
                    frameRate: { ideal: 30 }
                  },
                  audio: true
                });
                
                this.currentStream = stream;
                console.log('✅ Display capture started:', stream.id);
                
                // Create preview
                this.createPreview(stream);
                
                // Send message back to CDP
                console.log('📡 Stream active with tracks:', stream.getTracks().length);
                
                return stream;
              } catch (err) {
                console.error('❌ Display capture failed:', err);
                throw err;
              }
            },
            
            createPreview(stream) {
              // Remove existing preview
              const existing = document.getElementById('cdp-stream-preview');
              if (existing) existing.remove();
              
              const video = document.createElement('video');
              video.id = 'cdp-stream-preview';
              video.srcObject = stream;
              video.autoplay = true;
              video.muted = true;
              video.style.cssText = \`
                position: fixed;
                top: 20px;
                right: 20px;
                width: 320px;
                height: 180px;
                border: 3px solid #ff0000;
                border-radius: 8px;
                z-index: 999999;
                box-shadow: 0 4px 12px rgba(255,0,0,0.4);
              \`;
              
              document.body.appendChild(video);
              console.log('📺 Preview video created');
              
              // Auto-remove when stream ends
              stream.getVideoTracks()[0].addEventListener('ended', () => {
                video.remove();
                console.log('🛑 Stream ended, preview removed');
              });
            },
            
            stopCapture() {
              if (this.currentStream) {
                this.currentStream.getTracks().forEach(track => track.stop());
                this.currentStream = null;
                console.log('⏹️ Stream stopped');
              }
            }
          };
          
          // Auto-start after page load
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
              setTimeout(() => {
                console.log('🎬 Auto-starting stream...');
                window.streamingManager.startDisplayCapture().catch(console.error);
              }, 1500);
            });
          } else {
            setTimeout(() => {
              console.log('🎬 Auto-starting stream...');
              window.streamingManager.startDisplayCapture().catch(console.error);
            }, 1500);
          }
          
          // Add control button
          const btn = document.createElement('button');
          btn.textContent = '🎥 Start Stream';
          btn.style.cssText = \`
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 999999;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
          \`;
          btn.onclick = () => window.streamingManager.startDisplayCapture();
          document.body.appendChild(btn);
        })();
      `;

      // Inject the script
      await this.cdp.Runtime.evaluate({
        expression: streamingScript,
        sessionId,
      });

      // Mark target as having streaming capability
      const targetInfo = this.targets.get(targetId);
      if (targetInfo) {
        targetInfo.hasStreaming = true;
      }

      console.log("✅ Streaming script injected successfully");
      return true;
    } catch (error) {
      console.error("❌ Failed to inject streaming script:", error);
      return false;
    }
  }

  async navigateTarget(targetId, sessionId, url) {
    try {
      console.log(`🧭 Navigating target ${targetId} to: ${url}`);

      await this.cdp.Page.navigate({
        url,
        sessionId,
      });

      console.log("✅ Navigation initiated");
      return true;
    } catch (error) {
      console.error("❌ Navigation failed:", error);
      return false;
    }
  }

  async handleNewTarget(targetInfo) {
    if (targetInfo.type === "page") {
      try {
        console.log(`🆕 New page target detected: ${targetInfo.url}`);

        // Attach to new target
        const { sessionId } = await this.cdp.Target.attachToTarget({
          targetId: targetInfo.targetId,
          flatten: true,
        });

        // Store target info
        this.targets.set(targetInfo.targetId, {
          sessionId,
          url: targetInfo.url,
          hasStreaming: false,
        });

        // Wait a bit for page to load, then inject streaming
        setTimeout(async () => {
          await this.injectStreamingScript(targetInfo.targetId, sessionId);
        }, 2000);
      } catch (error) {
        console.error("❌ Failed to handle new target:", error);
      }
    }
  }

  async activateTarget(targetId) {
    try {
      await this.cdp.Target.activateTarget({ targetId });
      console.log(`✅ Target ${targetId} activated`);
    } catch (error) {
      console.error(`❌ Failed to activate target ${targetId}:`, error);
    }
  }

  async listTargets() {
    try {
      const { targetInfos } = await this.cdp.Target.getTargets();
      console.log("📋 Current targets:");
      targetInfos.forEach((target) => {
        console.log(`  - ${target.type}: ${target.url} (${target.targetId})`);
      });
      return targetInfos;
    } catch (error) {
      console.error("❌ Failed to list targets:", error);
      return [];
    }
  }

  async cleanup() {
    if (this.cdp) {
      await this.cdp.close();
      console.log("🧹 CDP connection closed");
    }
  }
}

// Main execution
(async () => {
  const manager = new CDPStreamingManager();

  try {
    // Connect to CDP
    const connected = await manager.connect();
    if (!connected) {
      console.error("Failed to connect to CDP");
      return;
    }

    // List existing targets
    await manager.listTargets();

    // Create a new target and inject streaming
    const { targetId, sessionId } = await manager.createNewTarget();
    await manager.injectStreamingScript(targetId, sessionId);

    // Navigate to a test page
    await manager.navigateTarget(targetId, sessionId, "https://example.com");

    // Activate the target (bring to front)
    await manager.activateTarget(targetId);

    console.log("🚀 CDP Streaming POC ready!");
    console.log("📝 Instructions:");
    console.log("  - New target created and streaming script injected");
    console.log("  - Stream should auto-start after page loads");
    console.log("  - Try opening new tabs manually to test auto-injection");
    console.log("  - Press Ctrl+C to cleanup and exit");

    // Keep the process running
    process.on("SIGINT", async () => {
      console.log("\n🛑 Shutting down...");
      await manager.cleanup();
      process.exit(0);
    });
  } catch (error) {
    console.error("❌ Main execution failed:", error);
    await manager.cleanup();
  }
})();
