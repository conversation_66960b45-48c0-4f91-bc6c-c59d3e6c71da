<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Screen Streaming Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        button:hover {
            background: #45a049;
        }
        button.danger {
            background: #f44336;
        }
        button.danger:hover {
            background: #da190b;
        }
        #status {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        .demo-area {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Screen Streaming Test Page</h1>
        
        <div class="card">
            <h2>Manual Controls</h2>
            <button onclick="startScreenShare()">📺 Start Screen Share</button>
            <button onclick="stopScreenShare()" class="danger">⏹️ Stop Screen Share</button>
            <button onclick="openNewTab()">🆕 Open New Tab</button>
            <button onclick="openPopup()">🪟 Open Popup Window</button>
        </div>

        <div class="card">
            <h2>Status</h2>
            <div id="status">Ready to start streaming...</div>
        </div>

        <div class="demo-area">
            <h3>Demo Content</h3>
            <p>This is a test page to demonstrate screen streaming capabilities.</p>
            <p>When you start screen sharing, you should see a preview video in the top-right corner.</p>
            
            <div style="display: flex; gap: 20px; margin: 20px 0;">
                <div style="background: #ff6b6b; padding: 20px; border-radius: 10px; flex: 1;">
                    <h4>Red Box</h4>
                    <p>This is some content in a red box.</p>
                </div>
                <div style="background: #4ecdc4; padding: 20px; border-radius: 10px; flex: 1;">
                    <h4>Teal Box</h4>
                    <p>This is some content in a teal box.</p>
                </div>
                <div style="background: #45b7d1; padding: 20px; border-radius: 10px; flex: 1;">
                    <h4>Blue Box</h4>
                    <p>This is some content in a blue box.</p>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>Instructions</h2>
            <ol>
                <li>Click "Start Screen Share" to begin capturing your screen</li>
                <li>Select the browser tab or entire screen when prompted</li>
                <li>A preview video will appear in the top-right corner</li>
                <li>Try opening new tabs or popups to test auto-injection</li>
                <li>Use the stop button to end the stream</li>
            </ol>
        </div>
    </div>

    <script>
        let currentStream = null;
        let previewVideo = null;

        function updateStatus(message) {
            document.getElementById('status').textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            console.log(message);
        }

        async function startScreenShare() {
            try {
                updateStatus('Requesting screen share permission...');
                
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        mediaSource: 'screen',
                        width: { ideal: 1920 },
                        height: { ideal: 1080 },
                        frameRate: { ideal: 30 }
                    },
                    audio: true
                });

                currentStream = stream;
                updateStatus(`Screen sharing started! Stream ID: ${stream.id}`);

                // Create preview video
                createPreview(stream);

                // Handle stream end
                stream.getVideoTracks()[0].addEventListener('ended', () => {
                    updateStatus('Screen sharing ended by user');
                    cleanup();
                });

            } catch (err) {
                updateStatus(`Error: ${err.message}`);
                console.error('Screen share error:', err);
            }
        }

        function createPreview(stream) {
            // Remove existing preview
            if (previewVideo) {
                previewVideo.remove();
            }

            previewVideo = document.createElement('video');
            previewVideo.srcObject = stream;
            previewVideo.autoplay = true;
            previewVideo.muted = true;
            previewVideo.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                width: 300px;
                height: 200px;
                border: 3px solid #00ff00;
                border-radius: 8px;
                z-index: 1000;
                box-shadow: 0 4px 8px rgba(0,0,0,0.5);
            `;

            document.body.appendChild(previewVideo);
            updateStatus('Preview video created');
        }

        function stopScreenShare() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                cleanup();
                updateStatus('Screen sharing stopped manually');
            } else {
                updateStatus('No active stream to stop');
            }
        }

        function cleanup() {
            currentStream = null;
            if (previewVideo) {
                previewVideo.remove();
                previewVideo = null;
            }
        }

        function openNewTab() {
            const urls = [
                'https://google.com',
                'https://github.com',
                'https://stackoverflow.com',
                'https://developer.mozilla.org'
            ];
            const randomUrl = urls[Math.floor(Math.random() * urls.length)];
            window.open(randomUrl, '_blank');
            updateStatus(`Opened new tab: ${randomUrl}`);
        }

        function openPopup() {
            const popup = window.open(
                'https://example.com',
                'testPopup',
                'width=600,height=400,scrollbars=yes,resizable=yes'
            );
            updateStatus('Opened popup window');
        }

        // Auto-start demo (optional)
        window.addEventListener('load', () => {
            updateStatus('Page loaded - ready for streaming');
            
            // Uncomment the line below to auto-start screen sharing
            // setTimeout(() => startScreenShare(), 2000);
        });

        // Make functions available globally for Puppeteer injection
        window.startScreenShare = startScreenShare;
        window.stopScreenShare = stopScreenShare;
        window.openNewTab = openNewTab;
        window.openPopup = openPopup;
    </script>
</body>
</html>
