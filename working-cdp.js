import { CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

// Working CDP streaming example
(async () => {
  try {
    console.log("🔗 Connecting to Chrome...");
    
    // Connect to Chrome
    const cdp = new CDP();
    
    // Create new tab
    console.log("🎯 Creating new tab...");
    const { targetId } = await cdp.Target.createTarget({ url: "about:blank" });
    console.log(`✅ Created tab: ${targetId}`);
    
    // Attach to the tab
    const { sessionId } = await cdp.Target.attachToTarget({ 
      targetId, 
      flatten: true 
    });
    console.log(`🔗 Attached to session: ${sessionId}`);
    
    // Navigate to test page
    console.log("🧭 Navigating to example.com...");
    await cdp.Page.navigate({ url: "https://example.com" });
    
    // Activate the tab (bring to front)
    await cdp.Target.activateTarget({ targetId });
    console.log("✅ Tab activated");
    
    // Wait for page to load, then inject streaming script
    setTimeout(async () => {
      console.log("💉 Injecting getDisplayMedia script...");
      
      const streamScript = `
        (async () => {
          try {
            console.log('🚀 Starting getDisplayMedia...');
            
            const stream = await navigator.mediaDevices.getDisplayMedia({
              video: true,
              audio: true
            });
            
            console.log('✅ Got stream:', stream.id);
            
            // Create preview video
            const video = document.createElement('video');
            video.srcObject = stream;
            video.autoplay = true;
            video.muted = true;
            video.style.cssText = \`
              position: fixed;
              top: 20px;
              right: 20px;
              width: 300px;
              height: 200px;
              border: 3px solid #00ff00;
              border-radius: 8px;
              z-index: 999999;
              box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            \`;
            
            document.body.appendChild(video);
            console.log('📺 Preview video added');
            
            // Add stop button
            const stopBtn = document.createElement('button');
            stopBtn.textContent = '⏹️ Stop Stream';
            stopBtn.style.cssText = \`
              position: fixed;
              top: 230px;
              right: 20px;
              z-index: 999999;
              padding: 8px 12px;
              background: #ff4444;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
            \`;
            stopBtn.onclick = () => {
              stream.getTracks().forEach(track => track.stop());
              video.remove();
              stopBtn.remove();
              console.log('🛑 Stream stopped');
            };
            
            document.body.appendChild(stopBtn);
            
            // Auto-remove when stream ends
            stream.getVideoTracks()[0].addEventListener('ended', () => {
              video.remove();
              stopBtn.remove();
              console.log('🔚 Stream ended');
            });
            
          } catch (err) {
            console.error('❌ getDisplayMedia failed:', err);
            alert('Screen sharing failed: ' + err.message);
          }
        })();
      `;
      
      try {
        await cdp.Runtime.evaluate({ expression: streamScript });
        console.log("✅ Script injected successfully!");
      } catch (err) {
        console.error("❌ Script injection failed:", err);
      }
    }, 3000);
    
    // Listen for new targets (tabs/popups)
    cdp.Target.targetCreated = async (params) => {
      const { targetInfo } = params;
      console.log(`🆕 New target: ${targetInfo.type} - ${targetInfo.url}`);
      
      if (targetInfo.type === 'page') {
        try {
          // Attach to new target
          const { sessionId: newSessionId } = await cdp.Target.attachToTarget({
            targetId: targetInfo.targetId,
            flatten: true,
          });
          
          console.log(`🔗 Attached to new target: ${newSessionId}`);
          
          // Wait a bit then inject streaming script
          setTimeout(async () => {
            try {
              console.log(`💉 Injecting into new tab: ${targetInfo.targetId}`);
              await cdp.Runtime.evaluate({ expression: streamScript });
              console.log("✅ New tab injection successful!");
            } catch (err) {
              console.error("❌ New tab injection failed:", err);
            }
          }, 2000);
          
        } catch (err) {
          console.error("❌ Failed to handle new target:", err);
        }
      }
    };
    
    // Enable target discovery
    await cdp.Target.setDiscoverTargets({ discover: true });
    
    console.log("\n🚀 CDP Streaming POC Ready!");
    console.log("📋 What's happening:");
    console.log("  ✓ New browser tab created and activated");
    console.log("  ✓ getDisplayMedia script will inject in 3 seconds");
    console.log("  ✓ New tabs will auto-get streaming capability");
    console.log("\n💡 Try:");
    console.log("  - Wait for auto-stream to start");
    console.log("  - Open new tabs manually (Ctrl+T)");
    console.log("  - Click the stop button to end streaming");
    console.log("\n⏹️  Press Ctrl+C to exit");
    
    // Keep the process running
    process.on('SIGINT', () => {
      console.log("\n🛑 Shutting down...");
      cdp.close();
      process.exit(0);
    });
    
  } catch (error) {
    console.error("❌ Error:", error);
    process.exit(1);
  }
})();
