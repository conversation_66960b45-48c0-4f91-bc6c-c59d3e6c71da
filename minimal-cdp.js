import { CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

// Minimal CDP example - just the essentials
(async () => {
  try {
    // Get CDP endpoint and connect
    const { webSocketDebuggerUrl } = await fetch(
      "http://localhost:9222/json/version"
    ).then((r) => r.json());
    const cdp = new CDP({ webSocketDebuggerUrl });

    // Create & attach to new target
    const { targetId } = await cdp.Target.createTarget({ url: "about:blank" });
    const { sessionId } = await cdp.Target.attachToTarget({
      targetId,
      flatten: true,
    });

    console.log(`✅ Created target ${targetId} with session ${sessionId}`);

    // Enable required domains (without sessionId for main connection)
    await cdp.Runtime.enable();
    await cdp.Page.enable();

    // Minimal getDisplayMedia script
    const script = `
      navigator.mediaDevices.getDisplayMedia({ video: true, audio: true })
        .then(stream => {
          console.log('✅ Stream:', stream.id);
          const v = document.createElement('video');
          v.srcObject = stream;
          v.autoplay = v.muted = true;
          v.style.cssText = 'position:fixed;top:10px;right:10px;width:250px;border:2px solid lime;z-index:9999';
          document.body.appendChild(v);
        })
        .catch(console.error);
    `;

    // Navigate and inject
    await cdp.Page.navigate({ url: "https://example.com" });
    await cdp.Target.activateTarget({ targetId });

    // Wait for page load then inject
    setTimeout(async () => {
      await cdp.Runtime.evaluate({ expression: script });
      console.log("🎥 Stream script injected! Check the browser.");
    }, 2000);

    // Handle new targets
    cdp.Target.targetCreated = async ({ targetInfo }) => {
      if (targetInfo.type === "page") {
        const { sessionId: newSession } = await cdp.Target.attachToTarget({
          targetId: targetInfo.targetId,
          flatten: true,
        });
        await cdp.Runtime.enable({ sessionId: newSession });
        setTimeout(
          () =>
            cdp.Runtime.evaluate({ expression: script, sessionId: newSession }),
          1000
        );
        console.log(`🆕 Injected into new tab: ${targetInfo.url}`);
      }
    };

    await cdp.Target.setDiscoverTargets({ discover: true });
    console.log("🚀 Ready! Open new tabs to test auto-injection.");
  } catch (err) {
    console.error("❌", err);
  }
})();
