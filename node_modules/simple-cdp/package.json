{"name": "simple-cdp", "description": "JavaScript library to interact with the Chrome DevTools Protocol", "author": "<PERSON><PERSON>", "license": "MIT", "version": "1.8.6", "type": "module", "keywords": ["cdp", "chrome-devtools-protocol", "browser-automation"], "engines": {"deno": ">=1.4", "bun": ">=1.0"}, "module": "./mod.js", "types": "./mod.d.ts", "exports": {".": {"import": {"types": "./mod.d.ts", "default": "./mod.js"}}}, "repository": {"type": "git", "url": "git+https://github.com/gil<PERSON>-lormeau/simple-cdp.git"}, "bugs": {"url": "https://github.com/gildas-lorm<PERSON>/simple-cdp/issues"}}