import puppeteer from "puppeteer";

// Simple POC for screen/tab streaming with getDisplayMedia
(async () => {
  const browserWSEndpoint = await fetch("http://localhost:9222/json/version")
    .then((res) => res.json())
    .then((data) => data.webSocketDebuggerUrl);

  const browser = await puppeteer.connect({
    headless: false,
    defaultViewport: null,
    browserWSEndpoint: browserWSEndpoint,
  });

  const page = await browser.newPage();

  // Function to inject getDisplayMedia script into any page
  const injectDisplayMediaScript = async (targetPage) => {
    await targetPage.evaluateOnNewDocument(() => {
      // Simple getDisplayMedia setup
      window.startScreenShare = async () => {
        try {
          const stream = await navigator.mediaDevices.getDisplayMedia({
            video: { mediaSource: "screen" },
            audio: true,
          });

          console.log("Screen sharing started!", stream);

          // Create video element to show the stream
          const video = document.createElement("video");
          video.srcObject = stream;
          video.autoplay = true;
          video.style.position = "fixed";
          video.style.top = "10px";
          video.style.right = "10px";
          video.style.width = "300px";
          video.style.height = "200px";
          video.style.border = "2px solid red";
          video.style.zIndex = "9999";
          document.body.appendChild(video);

          // Stop sharing when stream ends
          stream.getVideoTracks()[0].addEventListener("ended", () => {
            console.log("Screen sharing stopped");
            video.remove();
          });

          return stream;
        } catch (err) {
          console.error("Error starting screen share:", err);
        }
      };

      // Auto-start screen sharing (for demo)
      window.addEventListener("load", () => {
        setTimeout(() => {
          console.log("Auto-starting screen share in 2 seconds...");
          window.startScreenShare();
        }, 2000);
      });
    });
  };

  // Inject script into main page
  await injectDisplayMediaScript(page);

  // Handle new tabs/windows
  browser.on("targetcreated", async (target) => {
    console.log("New target created:", target.type(), target.url());

    if (target.type() === "page") {
      try {
        const newPage = await target.page();
        console.log("👉 New page detected. URL:", newPage.url());

        // Inject getDisplayMedia script into new page
        await injectDisplayMediaScript(newPage);

        // Activate the new tab (bring it to front)
        await newPage.bringToFront();

        // Optional: Navigate to a specific URL or interact with the page
        newPage.on("framenavigated", async (frame) => {
          console.log("   [New Page] Navigated to:", frame.url());

          // Auto-trigger screen sharing on navigation
          setTimeout(async () => {
            try {
              await newPage.evaluate(() => {
                if (window.startScreenShare) {
                  window.startScreenShare();
                }
              });
            } catch (err) {
              console.log("Could not auto-start screen share:", err.message);
            }
          }, 1000);
        });
      } catch (err) {
        console.log("Error handling new page:", err.message);
      }
    }
  });

  // Handle popups
  page.on("popup", async (popup) => {
    console.log("Popup opened! URL:", popup.url());

    // Inject script into popup
    await injectDisplayMediaScript(popup);

    // Bring popup to front
    await popup.bringToFront();
  });

  // Start with a simple page
  await page.goto("https://example.com", { waitUntil: "networkidle2" });

  // Add a button to manually open new tabs for testing
  await page.evaluate(() => {
    const button = document.createElement("button");
    button.textContent = "Open New Tab";
    button.style.position = "fixed";
    button.style.top = "10px";
    button.style.left = "10px";
    button.style.zIndex = "9999";
    button.style.padding = "10px";
    button.style.backgroundColor = "blue";
    button.style.color = "white";
    button.onclick = () => {
      window.open("https://google.com", "_blank");
    };
    document.body.appendChild(button);
  });

  console.log("POC ready! Click 'Open New Tab' to test new tab streaming");
})();
