import puppeteer from "puppeteer";
import WebSocket from "ws";

(async () => {
  const browserWSEndpoint = await fetch("http://localhost:9222/json/version")
    .then((res) => res.json())
    .then((data) => data.webSocketDebuggerUrl);
  // Launch Chromium
  console.log({ browserWSEndpoint });
  const browser = await puppeteer.connect({
    headless: false, // so we can see what happens
    defaultViewport: null,
    browserWSEndpoint: browserWSEndpoint,
  });

  const page = await browser.newPage();

  // ---- LISTENERS ----

  // Detect new tabs/windows (targets)
  browser.on("targetcreated", async (target) => {
    console.log("New target created:", target.type(), target.url());
    console.log("opener is")
    console.log("TargetInfo target:", target);
    if (target.type() === "page") {
      const newPage = await target.page();
      console.log("👉 New page detected. URL:", newPage.url());

      // optional: attach further listeners
      newPage.on("framenavigated", (frame) => {
        console.log("   [New Page] Navigated to:", frame.url());
      });
    }
  });

  // Detect tabs/windows being closed
  browser.on("targetdestroyed", (target) => {
    console.log("Target destroyed (closed):", target.type(), target.url());
  });

  // Detect if the *current page* opens a popup (via window.open)
  page.on("popup", async (popup) => {
    console.log("Popup opened! URL:", popup.url());
    popup.on("framenavigated", (frame) => {
      console.log("   [Popup] Navigated to:", frame.url());
    });
  });

  // Detect navigation within the same tab
  page.on("framenavigated", (frame) => {
    if (frame === page.mainFrame()) {
      console.log("Main frame navigated to:", frame.url());
    }
  });

  // ---- NAVIGATE ----
  await page.goto("https://uber.com", { waitUntil: "networkidle2" });

  console.log("Browser opened and navigated to Uber.com");
})();
