import { CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

// Simple CDP streaming POC - very basic and clean
(async () => {
  let cdp, targetId, sessionId;

  try {
    // 1. Connect to CDP
    console.log("🔗 Connecting to CDP...");
    const response = await fetch("http://localhost:9222/json/version");
    const { webSocketDebuggerUrl } = await response.json();

    cdp = new CDP({ webSocketDebuggerUrl });
    console.log("✅ Connected to CDP");

    // 2. Create new target
    console.log("🎯 Creating new target...");
    const target = await cdp.Target.createTarget({ url: "about:blank" });
    targetId = target.targetId;
    console.log(`✅ Target created: ${targetId}`);

    // 3. Attach to target
    const { sessionId: session } = await cdp.Target.attachToTarget({
      targetId,
      flatten: true,
    });
    sessionId = session;
    console.log(`🔗 Attached to session: ${sessionId}`);

    // 4. Enable domains
    await cdp.Runtime.enable({ sessionId });
    await cdp.Page.enable({ sessionId });
    console.log("✅ Domains enabled");

    // 5. Simple getDisplayMedia injection
    const simpleStreamScript = `
      // Simple streaming setup
      window.startStream = async () => {
        try {
          const stream = await navigator.mediaDevices.getDisplayMedia({
            video: true,
            audio: true
          });
          
          console.log('🎥 Stream started!', stream.id);
          
          // Create simple preview
          const video = document.createElement('video');
          video.srcObject = stream;
          video.autoplay = true;
          video.muted = true;
          video.style.cssText = 'position:fixed;top:10px;right:10px;width:300px;border:2px solid red;z-index:9999';
          document.body.appendChild(video);
          
          return stream;
        } catch (err) {
          console.error('Stream error:', err);
        }
      };
      
      // Auto-start
      setTimeout(() => {
        console.log('Auto-starting stream...');
        window.startStream();
      }, 2000);
      
      console.log('🚀 Stream script loaded!');
    `;

    // 6. Inject script
    console.log("💉 Injecting stream script...");
    await cdp.Runtime.evaluate({
      expression: simpleStreamScript,
      sessionId,
    });

    // 7. Navigate to test page
    console.log("🧭 Navigating to test page...");
    await cdp.Page.navigate({
      url: "https://example.com",
      sessionId,
    });

    // 8. Activate target (bring to front)
    await cdp.Target.activateTarget({ targetId });
    console.log("✅ Target activated");

    // 9. Listen for new targets (new tabs/popups)
    cdp.Target.targetCreated = async (params) => {
      const { targetInfo } = params;
      console.log(`🆕 New target: ${targetInfo.type} - ${targetInfo.url}`);

      if (targetInfo.type === "page") {
        try {
          // Attach to new target
          const { sessionId: newSession } = await cdp.Target.attachToTarget({
            targetId: targetInfo.targetId,
            flatten: true,
          });

          // Enable domains for new session
          await cdp.Runtime.enable({ sessionId: newSession });
          await cdp.Page.enable({ sessionId: newSession });

          // Inject streaming script into new target
          setTimeout(async () => {
            console.log(`💉 Injecting into new target: ${targetInfo.targetId}`);
            await cdp.Runtime.evaluate({
              expression: simpleStreamScript,
              sessionId: newSession,
            });

            // Activate new target
            await cdp.Target.activateTarget({ targetId: targetInfo.targetId });
          }, 1000);
        } catch (err) {
          console.error("Error handling new target:", err);
        }
      }
    };

    // Enable target discovery
    await cdp.Target.setDiscoverTargets({ discover: true });

    console.log("\n🚀 Simple CDP Streaming POC Ready!");
    console.log("📋 What's happening:");
    console.log("  ✓ New browser tab created");
    console.log("  ✓ getDisplayMedia script injected");
    console.log("  ✓ Auto-streaming will start in 2 seconds");
    console.log("  ✓ New tabs will auto-get streaming capability");
    console.log("\n💡 Try:");
    console.log("  - Wait for auto-stream to start");
    console.log("  - Open new tabs manually (Ctrl+T)");
    console.log("  - Use window.startStream() in console");
    console.log("\n⏹️  Press Ctrl+C to stop");

    // Keep running
    process.on("SIGINT", async () => {
      console.log("\n🛑 Cleaning up...");
      if (cdp) await cdp.close();
      process.exit(0);
    });
  } catch (error) {
    console.error("❌ Error:", error);
    if (cdp) await cdp.close();
    process.exit(1);
  }
})();
