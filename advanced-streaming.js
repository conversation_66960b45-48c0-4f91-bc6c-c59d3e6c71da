import puppeteer from "puppeteer";

// Advanced POC: Screen streaming with WebRTC and tab management
(async () => {
  const browserWSEndpoint = await fetch("http://localhost:9222/json/version")
    .then((res) => res.json())
    .then((data) => data.webSocketDebuggerUrl);
  
  const browser = await puppeteer.connect({
    headless: false,
    defaultViewport: null,
    browserWSEndpoint: browserWSEndpoint,
  });

  const page = await browser.newPage();

  // Advanced script injection with WebRTC streaming
  const injectAdvancedStreamingScript = async (targetPage) => {
    await targetPage.evaluateOnNewDocument(() => {
      // Create a simple WebRTC peer connection for streaming
      window.streamingUtils = {
        currentStream: null,
        peerConnection: null,
        
        async startDisplayCapture() {
          try {
            const stream = await navigator.mediaDevices.getDisplayMedia({
              video: {
                mediaSource: 'screen',
                width: { ideal: 1920 },
                height: { ideal: 1080 },
                frameRate: { ideal: 30 }
              },
              audio: {
                echoCancellation: true,
                noiseSuppression: true
              }
            });
            
            this.currentStream = stream;
            console.log('🎥 Display capture started:', stream.id);
            
            // Create preview video
            this.createPreview(stream);
            
            // Setup WebRTC for potential streaming
            this.setupWebRTC(stream);
            
            return stream;
          } catch (err) {
            console.error('❌ Display capture failed:', err);
            throw err;
          }
        },
        
        createPreview(stream) {
          // Remove existing preview
          const existing = document.getElementById('stream-preview');
          if (existing) existing.remove();
          
          const video = document.createElement('video');
          video.id = 'stream-preview';
          video.srcObject = stream;
          video.autoplay = true;
          video.muted = true;
          video.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 320px;
            height: 180px;
            border: 3px solid #00ff00;
            border-radius: 8px;
            z-index: 10000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
          `;
          
          // Add controls
          const controls = document.createElement('div');
          controls.style.cssText = `
            position: fixed;
            top: 210px;
            right: 20px;
            z-index: 10001;
          `;
          
          const stopBtn = document.createElement('button');
          stopBtn.textContent = '⏹️ Stop';
          stopBtn.style.cssText = 'margin-right: 5px; padding: 5px 10px;';
          stopBtn.onclick = () => this.stopCapture();
          
          const recordBtn = document.createElement('button');
          recordBtn.textContent = '🔴 Record';
          recordBtn.style.cssText = 'padding: 5px 10px;';
          recordBtn.onclick = () => this.startRecording();
          
          controls.appendChild(stopBtn);
          controls.appendChild(recordBtn);
          
          document.body.appendChild(video);
          document.body.appendChild(controls);
          
          // Auto-remove when stream ends
          stream.getVideoTracks()[0].addEventListener('ended', () => {
            video.remove();
            controls.remove();
            console.log('🛑 Stream ended, preview removed');
          });
        },
        
        setupWebRTC(stream) {
          this.peerConnection = new RTCPeerConnection({
            iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
          });
          
          // Add stream to peer connection
          stream.getTracks().forEach(track => {
            this.peerConnection.addTrack(track, stream);
          });
          
          console.log('📡 WebRTC peer connection ready');
        },
        
        async startRecording() {
          if (!this.currentStream) return;
          
          const mediaRecorder = new MediaRecorder(this.currentStream, {
            mimeType: 'video/webm;codecs=vp9'
          });
          
          const chunks = [];
          mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) chunks.push(event.data);
          };
          
          mediaRecorder.onstop = () => {
            const blob = new Blob(chunks, { type: 'video/webm' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `screen-recording-${Date.now()}.webm`;
            a.click();
            console.log('💾 Recording saved');
          };
          
          mediaRecorder.start();
          console.log('🔴 Recording started');
          
          // Stop recording after 10 seconds (for demo)
          setTimeout(() => {
            mediaRecorder.stop();
          }, 10000);
        },
        
        stopCapture() {
          if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => track.stop());
            this.currentStream = null;
          }
          if (this.peerConnection) {
            this.peerConnection.close();
            this.peerConnection = null;
          }
          console.log('🛑 Capture stopped');
        }
      };
      
      // Auto-start on page load
      window.addEventListener('load', () => {
        setTimeout(() => {
          console.log('🚀 Auto-starting display capture...');
          window.streamingUtils.startDisplayCapture().catch(console.error);
        }, 1500);
      });
      
      // Expose global function for manual trigger
      window.startStreaming = () => window.streamingUtils.startDisplayCapture();
      window.stopStreaming = () => window.streamingUtils.stopCapture();
    });
  };

  // Inject into main page
  await injectAdvancedStreamingScript(page);

  // Enhanced new tab handling
  browser.on("targetcreated", async (target) => {
    console.log("🆕 New target:", target.type(), target.url());
    
    if (target.type() === "page") {
      try {
        const newPage = await target.page();
        console.log("📄 New page ready:", newPage.url());
        
        // Inject streaming capabilities
        await injectAdvancedStreamingScript(newPage);
        
        // Activate and focus the new tab
        await newPage.bringToFront();
        await newPage.focus();
        
        // Wait for navigation and auto-start streaming
        newPage.on("framenavigated", async (frame) => {
          if (frame === newPage.mainFrame()) {
            console.log("🧭 Navigation:", frame.url());
            
            // Delay to ensure page is ready
            setTimeout(async () => {
              try {
                await newPage.evaluate(() => {
                  if (window.startStreaming) {
                    window.startStreaming();
                  }
                });
              } catch (err) {
                console.log("⚠️ Auto-stream failed:", err.message);
              }
            }, 2000);
          }
        });
        
      } catch (err) {
        console.error("❌ New page error:", err);
      }
    }
  });

  // Enhanced popup handling
  page.on("popup", async (popup) => {
    console.log("🪟 Popup detected:", popup.url());
    await injectAdvancedStreamingScript(popup);
    await popup.bringToFront();
  });

  // Navigate to test page
  await page.goto("https://example.com", { waitUntil: "networkidle2" });

  // Add enhanced UI controls
  await page.evaluate(() => {
    const controlPanel = document.createElement('div');
    controlPanel.style.cssText = `
      position: fixed;
      top: 20px;
      left: 20px;
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 15px;
      border-radius: 8px;
      z-index: 10000;
      font-family: Arial, sans-serif;
    `;
    
    controlPanel.innerHTML = `
      <h3>🎥 Streaming POC Controls</h3>
      <button id="openTab" style="margin: 5px; padding: 8px 12px;">🆕 Open New Tab</button><br>
      <button id="openPopup" style="margin: 5px; padding: 8px 12px;">🪟 Open Popup</button><br>
      <button id="startStream" style="margin: 5px; padding: 8px 12px;">▶️ Start Stream</button><br>
      <button id="stopStream" style="margin: 5px; padding: 8px 12px;">⏹️ Stop Stream</button>
    `;
    
    document.body.appendChild(controlPanel);
    
    // Event handlers
    document.getElementById('openTab').onclick = () => {
      window.open('https://google.com', '_blank');
    };
    
    document.getElementById('openPopup').onclick = () => {
      window.open('https://github.com', 'popup', 'width=800,height=600');
    };
    
    document.getElementById('startStream').onclick = () => {
      if (window.startStreaming) window.startStreaming();
    };
    
    document.getElementById('stopStream').onclick = () => {
      if (window.stopStreaming) window.stopStreaming();
    };
  });

  console.log("🚀 Advanced streaming POC ready!");
  console.log("📋 Features:");
  console.log("  - Auto-inject getDisplayMedia into new tabs/popups");
  console.log("  - WebRTC peer connection setup");
  console.log("  - Screen recording capability");
  console.log("  - Tab activation and focus management");
  console.log("  - Enhanced UI controls");
})();
