/**
 * Simple CDP Script - Browser automation using simple-cdp package
 *
 * This script demonstrates how to use the simple-cdp package to:
 * 1. Connect to a Chrome browser with remote debugging enabled
 * 2. Navigate to a webpage
 * 3. Execute JavaScript expressions
 * 4. Interact with page elements
 * 5. Handle auto-attached targets (iframes, new tabs, etc.)
 *
 * Prerequisites:
 * - Start Chrome with: chrome --remote-debugging-port=9222 --no-first-run --no-default-browser-check
 * - Or use: google-chrome --remote-debugging-port=9222 --no-first-run --no-default-browser-check
 */

import { createTarget, CDP, cdp } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

async function basicExample() {
  console.log("=== Basic Example ===");

  try {
    // Navigate to a webpage
    const url = "https://example.com";
    console.log(`Creating target for: ${url}`);
    const targetInfo = await createTarget(url);
    console.log("Target created:", targetInfo);

    // Create a CDP instance for the target
    const cdpInstance = new CDP(targetInfo);

    // Enable "Runtime" domain
    await cdpInstance.Runtime.enable();
    console.log("Runtime domain enabled");

    // Wait a moment for page to load
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Evaluate JavaScript expression
    const expression = "41 + 1";
    const { result } = await cdpInstance.Runtime.evaluate({ expression });
    console.log(`Result of "${expression}":`, result.value);

    // Get page title
    const titleResult = await cdpInstance.Runtime.evaluate({
      expression: "document.title",
    });
    console.log("Page title:", titleResult.result.value);

    // Get page URL
    const urlResult = await cdpInstance.Runtime.evaluate({
      expression: "window.location.href",
    });
    console.log("Current URL:", urlResult.result.value);

    // Get all links on the page
    const linksResult = await cdpInstance.Runtime.evaluate({
      expression:
        "Array.from(document.querySelectorAll('a')).map(a => ({text: a.textContent.trim(), href: a.href})).filter(link => link.text && link.href)",
    });
    console.log("Links found:", linksResult.result.value);

    console.log("Basic example completed successfully");
  } catch (error) {
    console.error("Error in basic example:", error);
  }
}

async function advancedExample() {
  console.log("\n=== Advanced Example with Auto-Attach ===");

  try {
    // Enable auto-attach to new targets
    await cdp.Target.setAutoAttach({
      autoAttach: true,
      flatten: true,
      waitForDebuggerOnStart: false,
    });
    console.log("Auto-attach enabled");

    // Add event listener for attached targets
    cdp.Target.addEventListener("attachedToTarget", onAttachedToTarget);
    console.log("Event listener added for attachedToTarget");

    // Create a new target and navigate to a test page
    const url = "https://httpbin.org/html";
    console.log(`Creating target for: ${url}`);
    await cdp.Target.createTarget({ url });

    // Wait for target to be created and attached
    await new Promise((resolve) => setTimeout(resolve, 3000));

    console.log("Advanced example setup completed");
  } catch (error) {
    console.error("Error in advanced example:", error);
  }
}

async function onAttachedToTarget({ params }) {
  console.log("Target attached:", params.targetInfo);

  try {
    // Get session ID
    const { sessionId, targetInfo } = params;

    // Check if the target is a page
    if (targetInfo.type === "page") {
      console.log(`Processing page target: ${targetInfo.url}`);

      // Enable "Runtime" domain for this session
      await cdp.Runtime.enable(null, sessionId);
      console.log("Runtime enabled for session:", sessionId);

      // Enable "Page" domain to handle navigation events
      await cdp.Page.enable(null, sessionId);
      console.log("Page domain enabled for session:", sessionId);

      // Wait for page to load
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Evaluate JavaScript expression
      const expression = "Math.random() * 100";
      const { result } = await cdp.Runtime.evaluate({ expression }, sessionId);
      console.log(`Random number generated: ${result.value}`);

      // Get page information
      const pageInfo = await cdp.Runtime.evaluate(
        {
          expression: `({
                    title: document.title,
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                    elementCount: document.querySelectorAll('*').length,
                    hasImages: document.querySelectorAll('img').length > 0,
                    hasLinks: document.querySelectorAll('a').length > 0
                })`,
        },
        sessionId
      );

      console.log("Page information:", pageInfo.result.value);

      // Try to interact with page elements
      const interactionResult = await cdp.Runtime.evaluate(
        {
          expression: `
                    // Find all clickable elements
                    const clickableElements = Array.from(document.querySelectorAll('a, button, input[type="button"], input[type="submit"]'));
                    const elementInfo = clickableElements.slice(0, 5).map(el => ({
                        tagName: el.tagName,
                        text: el.textContent?.trim() || el.value || '',
                        href: el.href || '',
                        id: el.id || '',
                        className: el.className || ''
                    }));
                    
                    // Return summary
                    ({
                        totalClickableElements: clickableElements.length,
                        firstFiveElements: elementInfo,
                        pageReady: document.readyState
                    });
                `,
        },
        sessionId
      );

      console.log("Interaction analysis:", interactionResult.result.value);
    } else {
      console.log(`Skipping non-page target: ${targetInfo.type}`);
    }
  } catch (error) {
    console.error("Error in onAttachedToTarget:", error);
  }
}

async function demonstratePageNavigation() {
  console.log("\n=== Page Navigation Example ===");

  try {
    // Create target for a simple page
    const url = "https://httpbin.org/";
    const targetInfo = await createTarget(url);
    const cdpInstance = new CDP(targetInfo);

    // Enable necessary domains
    await cdpInstance.Runtime.enable();
    await cdpInstance.Page.enable();
    console.log("Domains enabled");

    // Wait for initial page load
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Navigate to a different page
    console.log("Navigating to /json endpoint...");
    await cdpInstance.Page.navigate({ url: "https://httpbin.org/json" });

    // Wait for navigation
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Get the JSON content
    const jsonContent = await cdpInstance.Runtime.evaluate({
      expression: "document.body.textContent",
    });

    console.log("JSON response:", jsonContent.result.value);

    // Go back to the original page
    console.log("Going back...");
    await cdpInstance.Page.navigate({ url: "https://httpbin.org/" });

    await new Promise((resolve) => setTimeout(resolve, 2000));

    const backPageTitle = await cdpInstance.Runtime.evaluate({
      expression: "document.title",
    });

    console.log("Back to page with title:", backPageTitle.result.value);
  } catch (error) {
    console.error("Error in page navigation example:", error);
  }
}

async function demonstrateInputDispatching() {
  console.log("\n=== Input Dispatching Example ===");

  try {
    // Create target for a page with forms
    const url = "https://httpbin.org/forms/post";
    console.log(`Creating target for form page: ${url}`);
    const targetInfo = await createTarget(url);
    const cdpInstance = new CDP(targetInfo);

    // Enable necessary domains
    await cdpInstance.Runtime.enable();
    await cdpInstance.Page.enable();
    await cdpInstance.DOM.enable();
    console.log("All domains enabled for input dispatching");

    // Wait for page to load
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Get page information
    const pageInfo = await cdpInstance.Runtime.evaluate({
      expression: `JSON.stringify({
        title: document.title,
        url: window.location.href,
        formElements: {
          inputs: document.querySelectorAll('input').length,
          textareas: document.querySelectorAll('textarea').length,
          buttons: document.querySelectorAll('button, input[type="submit"]').length
        }
      })`,
    });
    const pageData = pageInfo.result.value
      ? JSON.parse(pageInfo.result.value)
      : null;
    console.log("Page loaded:", pageData || "Unable to get page info");

    // Demonstrate mouse click on first input field
    console.log("\n--- Mouse Click Example ---");
    const inputElement = await cdpInstance.Runtime.evaluate({
      expression: `
        JSON.stringify((() => {
          // Try multiple selectors to find an input field
          const selectors = [
            'input[name="custname"]',
            'input[type="text"]',
            'input:not([type="hidden"]):not([type="submit"]):not([type="button"])',
            'input'
          ];

          let input = null;
          for (const selector of selectors) {
            input = document.querySelector(selector);
            if (input) break;
          }

          if (input) {
            const rect = input.getBoundingClientRect();
            return {
              found: true,
              x: rect.left + rect.width / 2,
              y: rect.top + rect.height / 2,
              name: input.name || 'unnamed',
              type: input.type || 'text',
              selector: selectors.find(s => document.querySelector(s) === input)
            };
          } else {
            return { found: false, availableInputs: document.querySelectorAll('input').length };
          }
        })())
      `,
    });

    const inputData = inputElement.result.value
      ? JSON.parse(inputElement.result.value)
      : null;

    if (inputData && inputData.found) {
      const { x, y, name, type, selector } = inputData;
      console.log(
        `Found input field: ${name} (${type}) at position (${x}, ${y}) using selector: ${selector}`
      );

      // Click on the input field
      await cdpInstance.Input.dispatchMouseEvent({
        type: "mousePressed",
        x: x,
        y: y,
        button: "left",
        clickCount: 1,
      });

      await cdpInstance.Input.dispatchMouseEvent({
        type: "mouseReleased",
        x: x,
        y: y,
        button: "left",
        clickCount: 1,
      });

      console.log("Mouse click dispatched successfully");

      // Wait a moment
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Demonstrate keyboard input
      console.log("\n--- Keyboard Input Example ---");
      const textToType = "John Doe";
      console.log(`Typing text: "${textToType}"`);

      // Type each character
      for (const char of textToType) {
        await cdpInstance.Input.dispatchKeyEvent({
          type: "keyDown",
          text: char,
        });
        await cdpInstance.Input.dispatchKeyEvent({
          type: "keyUp",
          text: char,
        });
        // Small delay between keystrokes for realism
        await new Promise((resolve) => setTimeout(resolve, 50));
      }

      console.log("Text typed successfully");

      // Verify the input was filled
      const inputValue = await cdpInstance.Runtime.evaluate({
        expression: `
          const input = document.querySelector('input[type="text"]') ||
                       document.querySelector('input:not([type="hidden"]):not([type="submit"]):not([type="button"])') ||
                       document.querySelector('input');
          input ? input.value : 'No input found'
        `,
      });
      console.log("Input field value:", inputValue.result.value);
    } else {
      const debugInfo = inputData || {};
      console.log(
        `Input field not found. Available inputs: ${
          debugInfo.availableInputs || "unknown"
        }`
      );
    }

    // Demonstrate Tab key navigation
    console.log("\n--- Tab Navigation Example ---");
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyDown",
      key: "Tab",
      code: "Tab",
    });
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyUp",
      key: "Tab",
      code: "Tab",
    });
    console.log("Tab key pressed - moved to next field");

    // Type in the next field (assuming it's the email field)
    await new Promise((resolve) => setTimeout(resolve, 200));
    const emailText = "<EMAIL>";
    console.log(`Typing email: "${emailText}"`);

    for (const char of emailText) {
      await cdpInstance.Input.dispatchKeyEvent({
        type: "keyDown",
        text: char,
      });
      await cdpInstance.Input.dispatchKeyEvent({
        type: "keyUp",
        text: char,
      });
      await new Promise((resolve) => setTimeout(resolve, 30));
    }

    console.log("Email typed successfully");

    // Demonstrate special keys (Enter, Escape, etc.)
    console.log("\n--- Special Keys Example ---");

    // Press Escape key
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyDown",
      key: "Escape",
      code: "Escape",
    });
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyUp",
      key: "Escape",
      code: "Escape",
    });
    console.log("Escape key pressed");

    // Demonstrate mouse wheel scrolling
    console.log("\n--- Mouse Wheel Scrolling Example ---");
    await cdpInstance.Input.dispatchMouseEvent({
      type: "mouseWheel",
      x: 400,
      y: 300,
      deltaX: 0,
      deltaY: -120, // Scroll up
    });
    console.log("Mouse wheel scroll dispatched");

    // Get final form state
    const finalFormState = await cdpInstance.Runtime.evaluate({
      expression: `
        JSON.stringify(
          Array.from(document.querySelectorAll('input')).map(input => ({
            name: input.name,
            type: input.type,
            value: input.value,
            focused: document.activeElement === input
          }))
        )
      `,
    });
    const formData = finalFormState.result.value
      ? JSON.parse(finalFormState.result.value)
      : [];
    console.log("Final form state:", formData);

    console.log("Input dispatching example completed successfully");
  } catch (error) {
    console.error("Error in input dispatching example:", error);
  }
}

async function demonstrateAdvancedInputs() {
  console.log("\n=== Advanced Input Dispatching Example ===");

  try {
    // Create target for a more complex page
    const url = "https://httpbin.org/";
    console.log(`Creating target for advanced inputs: ${url}`);
    const targetInfo = await createTarget(url);
    const cdpInstance = new CDP(targetInfo);

    // Enable necessary domains
    await cdpInstance.Runtime.enable();
    await cdpInstance.Page.enable();
    await cdpInstance.DOM.enable();
    console.log("All domains enabled for advanced input dispatching");

    // Wait for page to load
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Demonstrate keyboard shortcuts
    console.log("\n--- Keyboard Shortcuts Example ---");

    // Ctrl+A (Select All)
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyDown",
      key: "Control",
      code: "ControlLeft",
      modifiers: 2, // Ctrl modifier
    });
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyDown",
      key: "a",
      code: "KeyA",
      modifiers: 2,
    });
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyUp",
      key: "a",
      code: "KeyA",
      modifiers: 2,
    });
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyUp",
      key: "Control",
      code: "ControlLeft",
    });
    console.log("Ctrl+A (Select All) dispatched");

    await new Promise((resolve) => setTimeout(resolve, 500));

    // Demonstrate mouse drag operation
    console.log("\n--- Mouse Drag Example ---");
    const startX = 100;
    const startY = 200;
    const endX = 300;
    const endY = 400;

    // Mouse down at start position
    await cdpInstance.Input.dispatchMouseEvent({
      type: "mousePressed",
      x: startX,
      y: startY,
      button: "left",
      clickCount: 1,
    });

    // Mouse move to end position (drag)
    await cdpInstance.Input.dispatchMouseEvent({
      type: "mouseMoved",
      x: endX,
      y: endY,
      button: "left",
    });

    // Mouse up at end position
    await cdpInstance.Input.dispatchMouseEvent({
      type: "mouseReleased",
      x: endX,
      y: endY,
      button: "left",
      clickCount: 1,
    });

    console.log(
      `Mouse drag from (${startX}, ${startY}) to (${endX}, ${endY}) completed`
    );

    // Demonstrate right-click context menu
    console.log("\n--- Right Click Example ---");
    await cdpInstance.Input.dispatchMouseEvent({
      type: "mousePressed",
      x: 200,
      y: 300,
      button: "right",
      clickCount: 1,
    });
    await cdpInstance.Input.dispatchMouseEvent({
      type: "mouseReleased",
      x: 200,
      y: 300,
      button: "right",
      clickCount: 1,
    });
    console.log("Right-click context menu triggered");

    await new Promise((resolve) => setTimeout(resolve, 500));

    // Demonstrate double-click
    console.log("\n--- Double Click Example ---");
    await cdpInstance.Input.dispatchMouseEvent({
      type: "mousePressed",
      x: 250,
      y: 250,
      button: "left",
      clickCount: 1,
    });
    await cdpInstance.Input.dispatchMouseEvent({
      type: "mouseReleased",
      x: 250,
      y: 250,
      button: "left",
      clickCount: 1,
    });
    await cdpInstance.Input.dispatchMouseEvent({
      type: "mousePressed",
      x: 250,
      y: 250,
      button: "left",
      clickCount: 2,
    });
    await cdpInstance.Input.dispatchMouseEvent({
      type: "mouseReleased",
      x: 250,
      y: 250,
      button: "left",
      clickCount: 2,
    });
    console.log("Double-click dispatched");

    // Demonstrate function keys
    console.log("\n--- Function Keys Example ---");
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyDown",
      key: "F12",
      code: "F12",
    });
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyUp",
      key: "F12",
      code: "F12",
    });
    console.log("F12 key pressed (Developer Tools)");

    await new Promise((resolve) => setTimeout(resolve, 500));

    // Demonstrate arrow keys navigation
    console.log("\n--- Arrow Keys Navigation Example ---");
    const arrowKeys = ["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"];

    for (const arrow of arrowKeys) {
      await cdpInstance.Input.dispatchKeyEvent({
        type: "keyDown",
        key: arrow,
        code: arrow,
      });
      await cdpInstance.Input.dispatchKeyEvent({
        type: "keyUp",
        key: arrow,
        code: arrow,
      });
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
    console.log("Arrow keys navigation completed");

    // Demonstrate modifier combinations
    console.log("\n--- Modifier Key Combinations Example ---");

    // Shift+Tab (reverse tab)
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyDown",
      key: "Shift",
      code: "ShiftLeft",
      modifiers: 8, // Shift modifier
    });
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyDown",
      key: "Tab",
      code: "Tab",
      modifiers: 8,
    });
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyUp",
      key: "Tab",
      code: "Tab",
      modifiers: 8,
    });
    await cdpInstance.Input.dispatchKeyEvent({
      type: "keyUp",
      key: "Shift",
      code: "ShiftLeft",
    });
    console.log("Shift+Tab (reverse tab) dispatched");

    console.log("Advanced input dispatching example completed successfully");
  } catch (error) {
    console.error("Error in advanced input dispatching example:", error);
  }
}

async function main() {
  console.log("Simple CDP Script Starting...");
  console.log(
    "Make sure Chrome is running with: chrome --remote-debugging-port=9222\n"
  );

  try {
    // Run basic example
    await basicExample();

    // Run advanced example with auto-attach
    await advancedExample();

    // Demonstrate page navigation
    await demonstratePageNavigation();

    // Demonstrate input dispatching
    await demonstrateInputDispatching();

    // Demonstrate advanced input dispatching
    await demonstrateAdvancedInputs();

    console.log("\n=== All examples completed ===");
  } catch (error) {
    console.error("Main execution error:", error);
  }
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log("\nShutting down gracefully...");
  process.exit(0);
});

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export {
  basicExample,
  advancedExample,
  demonstratePageNavigation,
  demonstrateInputDispatching,
  demonstrateAdvancedInputs,
  onAttachedToTarget,
};
