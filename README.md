# Screen/Tab Streaming POC with Chrome DevTools Protocol

This repository demonstrates various approaches to screen streaming using Chrome DevTools Protocol (CDP) and `getDisplayMedia` API.

## 🎯 What This POC Demonstrates

### Core Capabilities
- ✅ **Auto-inject getDisplayMedia** into newly opened tabs/popups
- ✅ **Activate and focus** new browser tabs programmatically  
- ✅ **Pure CDP implementation** using simple-cdp package
- ✅ **Screen capture with preview** video element
- ✅ **Tab management** - detect, attach, and control new targets
- ✅ **Cross-tab streaming** - every new tab gets streaming capability

### Key Features
1. **New Tab Detection**: Automatically detects when user opens new tabs
2. **Script Injection**: Injects getDisplayMedia code into every new page
3. **Tab Activation**: Brings new tabs to the foreground
4. **Auto-streaming**: Can auto-start screen sharing on page load
5. **Preview Display**: Shows captured screen in a floating video element

## 📁 Files Overview

### 1. `minimal-cdp.js` - **Start Here!** 
The simplest possible implementation (50 lines):
```bash
node minimal-cdp.js
```
- Creates new browser tab
- Injects basic getDisplayMedia script
- Auto-detects and injects into new tabs

### 2. `simple-cdp-streaming.js` - Enhanced Version
More features with clean code (100 lines):
```bash
node simple-cdp-streaming.js
```
- Better error handling
- Auto-start streaming
- Detailed logging
- Target management

### 3. `cdp-streaming.js` - Full-Featured
Complete implementation with class structure:
```bash
node cdp-streaming.js
```
- Object-oriented design
- Advanced target tracking
- Multiple streaming options
- Comprehensive error handling

### 4. `index.js` - Puppeteer Version
Original Puppeteer-based implementation:
```bash
node index.js
```
- Uses Puppeteer API
- Event-driven architecture
- Enhanced UI controls

## 🚀 Quick Start

### Prerequisites
1. **Chrome/Chromium** running with remote debugging:
   ```bash
   # macOS
   /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222 --user-data-dir=/tmp/chrome-debug

   # Linux
   google-chrome --remote-debugging-port=9222 --user-data-dir=/tmp/chrome-debug

   # Windows
   chrome.exe --remote-debugging-port=9222 --user-data-dir=c:\temp\chrome-debug
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

### Run the POC
```bash
# Start with the minimal example
node minimal-cdp.js
```

## 🎬 What Happens When You Run It

1. **Connects to Chrome** via CDP WebSocket
2. **Creates new browser tab** programmatically
3. **Injects getDisplayMedia script** into the page
4. **Navigates to example.com** 
5. **Auto-starts screen sharing** after 2 seconds
6. **Shows preview video** in top-right corner
7. **Monitors for new tabs** and auto-injects streaming code

## 🧪 Testing the POC

### Manual Testing
1. Run any of the scripts
2. Wait for auto-streaming to start
3. **Open new tabs** (Ctrl+T or Cmd+T)
4. **Open popups** via JavaScript
5. Notice streaming auto-starts in new tabs

### Browser Console Testing
In any injected tab, try:
```javascript
// Manual stream start
window.startStream()

// Check if streaming is available
console.log(typeof window.startStream)
```

## 🔧 Key CDP Operations Used

### Target Management
```javascript
// Create new browser tab
const { targetId } = await cdp.Target.createTarget({ url: "about:blank" });

// Attach to tab for control
const { sessionId } = await cdp.Target.attachToTarget({ targetId, flatten: true });

// Activate tab (bring to front)
await cdp.Target.activateTarget({ targetId });
```

### Script Injection
```javascript
// Enable Runtime domain
await cdp.Runtime.enable({ sessionId });

// Inject JavaScript
await cdp.Runtime.evaluate({
  expression: "console.log('Hello from CDP!')",
  sessionId
});
```

### Event Handling
```javascript
// Listen for new tabs/popups
cdp.Target.targetCreated = async ({ targetInfo }) => {
  if (targetInfo.type === 'page') {
    // Handle new page target
  }
};

// Enable target discovery
await cdp.Target.setDiscoverTargets({ discover: true });
```

## 🎥 getDisplayMedia Integration

### Basic Usage
```javascript
const stream = await navigator.mediaDevices.getDisplayMedia({
  video: { mediaSource: 'screen' },
  audio: true
});
```

### Advanced Options
```javascript
const stream = await navigator.mediaDevices.getDisplayMedia({
  video: {
    mediaSource: 'screen',
    width: { ideal: 1920 },
    height: { ideal: 1080 },
    frameRate: { ideal: 30 }
  },
  audio: {
    echoCancellation: true,
    noiseSuppression: true
  }
});
```

## 🔍 Troubleshooting

### Common Issues
1. **"CDP connection failed"**
   - Ensure Chrome is running with `--remote-debugging-port=9222`
   - Check if port 9222 is accessible

2. **"getDisplayMedia is not a function"**
   - Requires HTTPS or localhost
   - Check browser permissions

3. **"Permission denied"**
   - User must grant screen sharing permission
   - Some sites block getDisplayMedia

### Debug Tips
- Check Chrome DevTools Console for errors
- Use `chrome://inspect` to see remote debugging targets
- Monitor network tab for WebSocket connections

## 🎯 Use Cases

### Social Login Flows
- Capture OAuth popup windows
- Record authentication steps
- Monitor redirect chains

### Screen Recording
- Automated screen capture
- Tab-specific recording
- Multi-tab streaming

### Remote Assistance
- Share specific browser tabs
- Guide users through web flows
- Capture user interactions

## 🚧 Limitations

1. **User Permission Required**: getDisplayMedia always requires user interaction
2. **HTTPS Only**: Most browsers require secure context
3. **Browser Support**: Chrome/Edge/Firefox with slight differences
4. **Tab Visibility**: Some browsers limit background tab access

## 📚 References

- [Chrome DevTools Protocol](https://chromedevtools.github.io/devtools-protocol/)
- [getDisplayMedia API](https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getDisplayMedia)
- [simple-cdp Package](https://www.npmjs.com/package/simple-cdp)
- [Puppeteer Documentation](https://pptr.dev/)

---

**🎉 Happy Streaming!** This POC shows the foundation for building sophisticated browser automation and screen capture tools using pure CDP.
